import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:onekitty/screens/dashboard/pages/events/controllers/view_single_event.dart';
import 'package:onekitty/models/events/events_model.dart';
import 'package:onekitty/screens/dashboard/pages/events/views/screens/view_single_event_organizer.dart';
import 'package:onekitty/screens/dashboard/pages/events/views/screens/view_single_event_viewer.dart';
import 'package:onekitty/utils/date_formatter.dart';
import 'package:onekitty/screens/dashboard/pages/events/views/widgets/my_quill_editor.dart' show QuillEditorShortWidget;
import 'package:onekitty/utils/show_cached_network_image.dart';
import 'package:onekitty/main.dart' show isLight; 
import 'asset_urls.dart';
import 'event_details.dart';

// Custom page route for smoother hero animations
class HeroPageRoute<T> extends PageRouteBuilder<T> {
  final Widget page;
  
  HeroPageRoute({required this.page})
      : super(
          pageBuilder: (context, animation, secondaryAnimation) => page,
          transitionDuration: const Duration(milliseconds: 600),
          reverseTransitionDuration: const Duration(milliseconds: 500),
          transitionsBuilder: (context, animation, secondaryAnimation, child) {
            // Fade animation
            var fadeAnimation = CurvedAnimation(
              parent: animation,
              curve: Curves.easeInOut,
            );
            
            // Scale animation
            var scaleAnimation = Tween<double>(
              begin: 0.95,
              end: 1.0,
            ).animate(CurvedAnimation(
              parent: animation,
              curve: Curves.easeOutCubic,
            ));
            
            return FadeTransition(
              opacity: fadeAnimation,
              child: ScaleTransition(
                scale: scaleAnimation,
                child: child,
              ),
            );
          },
        );
}

class EventCards extends StatefulWidget {
  final bool? organizer;
  final Event event;
  const EventCards({super.key, required this.event, this.organizer});

  @override
  State<EventCards> createState() => _EventCardsState();
}

class _EventCardsState extends State<EventCards> with AutomaticKeepAliveClientMixin {
  // Cache computed values to avoid recalculating on every build
  late final String _imageUrl;
  late final String _locationText;
  late final String _dateTimeText;
  late final String _heroImageTag;
  late final String _heroTextTag;
  late final String _locationId;
  late final String _dateId;
  late final String _quillTag;

  @override
  bool get wantKeepAlive => true; // Keep widget alive to maintain state

  @override
  void initState() {
    super.initState();
    _precomputeValues();
  }

  void _precomputeValues() {
    final event = widget.event;

    // Precompute image URL
    _imageUrl = (event.eventMedia?.isNotEmpty == true && event.eventMedia![0].url?.isNotEmpty == true)
        ? event.eventMedia![0].url!
        : AssetUrl.onekittyBannnerUrl;

    // Precompute location text
    _locationText = "${event.locationTip} - ${event.venue}";

    // Precompute date/time text (only if startDate is not null)
    if (event.startDate != null) {
      final localDate = event.startDate!.toLocal();
      _dateTimeText = '${formatDate(localDate.toString())}, ${formatTime(localDate.toString())}';
    } else {
      _dateTimeText = 'Date TBD';
    }

    // Precompute hero tags and IDs
    final eventId = event.id ?? 0;
    _heroImageTag = 'image:$eventId';
    _heroTextTag = 'text:$eventId';
    _locationId = "location$eventId";
    _dateId = "date$eventId";
    _quillTag = 'event_$eventId';
  }

  void _navigateToEvent() {
    Get.put(ViewSingleEventController()).event(widget.event);
    Navigator.push(
      context,
      HeroPageRoute(
        page: ViewSingleEventOrganizer(
          eventmodel: MyEventsModel(event: widget.event),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    super.build(context); // Required for AutomaticKeepAliveClientMixin

    return GestureDetector(
      onTap: _navigateToEvent,
      child: ValueListenableBuilder<bool>(
        valueListenable: isLight,
        builder: (context, isLightTheme, child) {
          return Card(
            elevation: 4,
            margin: const EdgeInsets.all(8),
            color: isLightTheme ? Colors.white : const Color(0xff26262e),
            child: Padding(
              padding: EdgeInsets.only(
                left: 6.spMin,
                right: 6.spMin,
                top: 6.spMin,
                bottom: 10.spMin,
              ),
              child: Column(
                children: [
                  Hero(
                    tag: _heroImageTag,
                    child: ConstrainedBox(
                      constraints: BoxConstraints(
                        maxHeight: 300.h,
                      ),
                      child: AdaptiveCachedNetworkImage(
                        imageUrl: _imageUrl,
                        initialWidth: 390.w,
                        borderRadius: 8.r,
                        initialHeight: 300.h,
                      ),
                    ),
                  ),
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Expanded(
                        child: Material(
                          color: Colors.transparent,
                          textStyle: TextStyle(
                            color: Colors.grey.shade700,
                            fontWeight: FontWeight.w400,
                            fontSize: 14,
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Padding(
                                padding: const EdgeInsets.symmetric(vertical: 8.0),
                                child: Hero(
                                  tag: _heroTextTag,
                                  child: Material(
                                    color: Colors.transparent,
                                    child: Text(
                                      widget.event.title,
                                      maxLines: 2,
                                      style: TextStyle(
                                        color: isLightTheme ? Colors.black : Colors.white70,
                                        fontWeight: FontWeight.w700,
                                        fontSize: 20,
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                              EventDetailsWidget(
                                id: _locationId,
                                label: _locationText,
                                image: 'assets/images/icons/location.png',
                                icon: Icons.location_on_outlined,
                              ),
                              EventDetailsWidget(
                                id: _dateId,
                                image: 'assets/images/icons/calendar.png',
                                label: _dateTimeText,
                                icon: Icons.calendar_month,
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                  Align(
                    alignment: Alignment.topLeft,
                    child: QuillEditorShortWidget(
                      maxLines: 2,
                      text: widget.event.description,
                      tag: _quillTag,
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }
}
/*?
class RecommendedCardsWidget extends StatelessWidget {
  const RecommendedCardsWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Card(
      color: Colors.white,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(15), // Rounded corners
      ),
      elevation: 5,
      child: Padding(
        padding: const EdgeInsets.all(6.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                ClipRRect(
                  borderRadius: BorderRadius.circular(8.0),
                  child: Image.network(
                    'https://onewayeventproductions.com/wp-content/uploads/2020/01/AdobeStock_290777302.jpeg',
                    width: 120,
                    height: 120,
                    fit: BoxFit.cover,
                  ),
                ),
                const SizedBox(width: 12),
                const Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text('${CountryConfig.name} Awards',
                          style: TextStyle(
                              color: Colors.black,
                              fontWeight: FontWeight.w700,
                              fontSize: 20)),
                      EventDetailsWidget(
                          label: 'Lavington, Nairobi',
                          icon: Icons.location_on_outlined),
                      EventDetailsWidget(
                          label: '14 Feb 2024, 8:00AM',
                          icon: Icons.access_time_rounded),
                      EventDetailsWidget(
                          label: '122 Members', icon: Icons.people_outline)
                    ],
                  ),
                ),
              ],
            ),
            // SizedBox(height: 12),s
            const Text(
              'Pride of ${CountryConfig.name} Awards Galla night Event will be hosted by Tony Mwirigi. Be sure to turn up and enjoy.',
              style: TextStyle(fontSize: 16),
            ),
            // SizedBox(height: 12),
            Row(
              children: [
                const AttendeesWidget(
                  //TODO recommended widget
                  count: 0,
                  padding: 4,
                ),
                const Spacer(),
                OutlinedButton(
                    onPressed: () {},
                    child: const Padding(
                      padding: EdgeInsets.all(14.0),
                      child: Text('View'),
                    ))
              ],
            )
          ],
        ),
      ),
    );
  }
}
  1,2,3,4
*/