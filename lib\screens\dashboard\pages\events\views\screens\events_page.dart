import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:onekitty/screens/dashboard/pages/events/controllers/events_controller.dart';
import 'package:onekitty/helpers/connectivity_wrapper.dart';
import 'package:onekitty/screens/dashboard/pages/events/create_event/create_event_page.dart';
import 'package:onekitty/screens/dashboard/pages/events/views/widgets/attendees_widget.dart';
import 'package:onekitty/utils/app_bar/custom_app_bar.dart';
import 'package:onekitty/utils/event_cards.dart';
import 'package:onekitty/screens/dashboard/pages/events/views/widgets/my_event_card.dart';
import 'package:onekitty/screens/dashboard/pages/events/views/widgets/search_widget.dart';
import 'package:shimmer/shimmer.dart';

class EventsPage extends StatefulWidget {
  const EventsPage({super.key});

  @override
  State<EventsPage> createState() => _EventsPageState();
}

class _EventsPageState extends State<EventsPage> {
  final ValueNotifier<int> _eventTabNumber = ValueNotifier(1);
  final _controller = Get.put(Eventcontroller());
  final ScrollController scrollController = ScrollController();
  bool _isLoadingMore = false;

  @override
  void initState() {
    super.initState();
    _controller.loadEvents();
    _controller.loadUserEvents();
    scrollController.addListener(_scrollListener);
  }

  Future<void> _scrollListener() async {
    if (_isLoadingMore) return;

    if (scrollController.position.pixels >=
        scrollController.position.maxScrollExtent * 0.8) {
      _isLoadingMore = true;
      try {
        if (_eventTabNumber.value == 0) {
          await _controller.loadMoreEvents();
        } else {
          await _controller.loadMoreUserEvents();
        }
      } finally {
        _isLoadingMore = false;
      }
    }
  }

  @override
  void dispose() {
    scrollController.removeListener(_scrollListener);
    scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return ConnectivityCheck(
      child: Scaffold(
        body: RefreshIndicator(
          onRefresh: () async {
            if (_eventTabNumber.value == 0) {
              await _controller.refreshEvents();
            } else {
              await _controller.refreshUserEvents();
            }
          },
          child: CustomScrollView(
            controller: scrollController,
            slivers: [
              SliverAppBar(
                  backgroundColor: Theme.of(context).scaffoldBackgroundColor,
                  pinned: true,
                  expandedHeight: 200.h,
                  flexibleSpace: FlexibleSpaceBar(
                    background: Padding(
                      padding: EdgeInsets.symmetric(vertical:  8.0.h, horizontal: 20.w),
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text('events'.tr,
                              style: TextStyle(
                                  fontWeight: FontWeight.w700,
                                  fontSize: 20.spMin)),
                            FilledButton.icon( 
                            onPressed: () {
                              Navigator.push(
                                context,
                                MaterialPageRoute(
                                  builder: (context) =>
                                    const CreateEventPage()));
                            },
                            icon: const Icon(Icons.add),
                            label: Text('create_event'.tr),
                            )
                        ],
                      ),
                    ),
                  ),
                  bottom: PreferredSize(
                    preferredSize:   Size(double.infinity, 70.h),
                    child: ValueListenableBuilder(
                        valueListenable: _eventTabNumber,
                        builder: (context, eventTabNumber, _) {
                          return Padding(
                            padding: EdgeInsets.symmetric(vertical:  2.0.spMin, horizontal: 12.w),
                            child: SearchBarWidget(page: eventTabNumber),
                          );
                        }),
                  ),
                  title: buildAppBarWithImage(context)),
              SliverToBoxAdapter(
                child: SizedBox(
                  child: Center(
                    child: ValueListenableBuilder(
                        valueListenable: _eventTabNumber,
                        builder: (context, eventTabNumber, _) {
                          return Row(
                            children: [
                              TabButtonWidget(
                                  onTap: () => _eventTabNumber.value = 1,
                                  label: 'my_events'.tr,
                                  active: eventTabNumber == 1),
                              TabButtonWidget(
                                label: 'other_events'.tr,
                                active: eventTabNumber == 0,
                                onTap: () => { _eventTabNumber.value = 0},
                              ),
                            ],
                          );
                        }),
                  ),
                ),
              ),
              ValueListenableBuilder(
                valueListenable: _eventTabNumber,
                builder: (context, eventTabNumber, _) {
                  return SliverToBoxAdapter(
                      child: eventTabNumber == 0
                          ? Obx(() {
                              // Show initial loading for events
                              if (_controller.eventsLoading.value && _controller.events.isEmpty) {
                                return _buildInitialLoadingIndicator(true);
                              }

                              // Show empty state if no events
                              if (!_controller.eventsLoading.value && _controller.events.isEmpty) {
                                return _buildEmptyState('No events found', 'Try adjusting your search or filters');
                              }

                              return ListView.builder(
                                physics: const NeverScrollableScrollPhysics(),
                                shrinkWrap: true,
                                itemCount: _controller.events.length + 1,
                                itemBuilder: (context, index) {
                                  if (index == _controller.events.length) {
                                    return _buildLoadingIndicator();
                                  }
                                  return EventCards(
                                      event: _controller.events[index]);
                                },
                              );
                            })
                          : Obx(() {
                              // Show initial loading for user events
                              if (_controller.userEventsLoading.value && _controller.userEvents.isEmpty) {
                                return _buildInitialLoadingIndicator(false);
                              }

                              // Show empty state if no user events
                              if (!_controller.userEventsLoading.value && _controller.userEvents.isEmpty) {
                                return _buildEmptyState('No events created', 'Create your first event to get started');
                              }

                              return ListView.builder(
                                physics: const NeverScrollableScrollPhysics(),
                                shrinkWrap: true,
                                itemCount: _controller.userEvents.length + 1,
                                itemBuilder: (context, index) {
                                  if (index == _controller.userEvents.length) {
                                    return _buildLoadingIndicator();
                                  }
                                  return MyEventCards(
                                    eventModel: _controller.userEvents[index],
                                    organizer: true,
                                  );
                                },
                              );
                            }));
                },
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildLoadingIndicator() {
    return Obx(() {
      final isEventsTab = _eventTabNumber.value == 0;
      final isLoadingMore = isEventsTab
          ? _controller.eventsLoadingMore.value
          : _controller.userEventsLoadingMore.value;
      final hasMoreData = isEventsTab
          ? _controller.eventsHasMoreData.value
          : _controller.userEventsHasMoreData.value;

      if (isLoadingMore && hasMoreData) {
        return Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              const CircularProgressIndicator(),
              const SizedBox(height: 8),
              Text(
                isEventsTab ? 'Loading more events...' : 'Loading more user events...',
                style: const TextStyle(fontStyle: FontStyle.italic),
              ),
            ],
          ),
        );
      }

      // Show "no more data" message when reached the end
      if (!hasMoreData && (isEventsTab ? _controller.events.isNotEmpty : _controller.userEvents.isNotEmpty)) {
        return Padding(
          padding: const EdgeInsets.all(16.0),
          child: Text(
            isEventsTab ? 'No more events to load' : 'No more user events to load',
            textAlign: TextAlign.center,
            style: const TextStyle(fontStyle: FontStyle.italic),
          ),
        );
      }

      return const SizedBox.shrink();
    });
  }

  Widget _buildInitialLoadingIndicator(bool isEventsTab) {
    return Padding(
      padding: const EdgeInsets.all(32.0),
      child: Column(
        children: [
          const CircularProgressIndicator(),
          const SizedBox(height: 16),
          Text(
            isEventsTab ? 'Loading events...' : 'Loading your events...',
            style: const TextStyle(fontStyle: FontStyle.italic),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState(String title, String subtitle) {
    return Padding(
      padding: const EdgeInsets.all(32.0),
      child: Column(
        children: [
          Icon(
            Icons.event_busy,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            title,
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            subtitle,
            textAlign: TextAlign.center,
            style: TextStyle(
              color: Colors.grey[600],
              fontStyle: FontStyle.italic,
            ),
          ),
        ],
      ),
    );
  }

}

class EventShimmer extends StatelessWidget {
  final double? itemHeight;
  const EventShimmer({super.key, this.itemHeight});

  @override
  Widget build(BuildContext context) {
    return Shimmer.fromColors(
      baseColor: Colors.grey[300]!,
      highlightColor: Colors.grey[100]!,
      child: 
      ListView.builder(
          shrinkWrap: true,
          itemCount: 3,
          physics: const NeverScrollableScrollPhysics(),
          itemBuilder: (_, __) {
            if (__ == 2) {
              return const CupertinoActivityIndicator();
            }
            return Padding(
              padding: const EdgeInsets.only(bottom: 8.0),
              child: Container(
                height: itemHeight ?? 350.h,
                color: Colors.white,
              ),
            );
          }),
    );
  }
}
